<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM Modeling Metrics - Model Analysis Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/hardware-comparison.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-microchip me-2"></i>
                LLM Modeling Metrics
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#models">Models</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#hardware-comparison" data-section="hardware-comparison">Hardware Comparison</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#docs" target="_blank">API Docs</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Model Selection and Configuration Panel -->
        <div class="row">
            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            Model Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Model Selection -->
                        <div class="mb-4">
                            <label for="modelSelect" class="form-label">
                                <i class="fas fa-robot me-1"></i>
                                Select Models
                            </label>
                            <select id="modelSelect" class="form-select" multiple="multiple" style="width: 100%">
                                <!-- Options will be populated dynamically -->
                            </select>
                            <div class="form-text">
                                Search and select models from the list, or type custom model names (all models supported)
                            </div>
                            <div id="modelValidation" class="mt-2"></div>
                        </div>

                        <!-- Quick Model Presets -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-star me-1"></i>
                                Quick Presets
                            </label>
                            <div class="btn-group-vertical d-grid gap-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" data-preset="dense-comparison">
                                    Dense Models Comparison
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" data-preset="moe-comparison">
                                    MoE Models Comparison
                                </button>
                            </div>
                        </div>

                        <!-- Sequence Length Configuration -->
                        <div class="mb-3">
                            <label for="sequenceLength" class="form-label">
                                <i class="fas fa-ruler me-1"></i>
                                Sequence Length
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="sequenceLength" 
                                       value="2048" min="1" max="32768" step="1">
                                <div class="input-group-text">tokens</div>
                            </div>
                            <div class="form-text">Input sequence length for analysis</div>
                        </div>

                        <!-- Batch Size Configuration -->
                        <div class="mb-3">
                            <label for="batchSize" class="form-label">
                                <i class="fas fa-layer-group me-1"></i>
                                Batch Size
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="batchSize" 
                                       value="1" min="1" max="1024" step="1">
                                <div class="input-group-text">samples</div>
                            </div>
                            <div class="form-text">Batch size for memory calculations</div>
                        </div>

                        <!-- Hardware Selection -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">
                                    <i class="fas fa-microchip me-1"></i>
                                    Hardware Platform
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableHardwareAnalysis">
                                    <label class="form-check-label" for="enableHardwareAnalysis">
                                        Enable
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Hardware Selector Container -->
                            <div id="hardwareSelectorContainer" class="collapse">
                                <!-- Hardware selector component will be rendered here -->
                            </div>
                        </div>

                        <!-- Mixed Precision Configuration -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">
                                    <i class="fas fa-calculator me-1"></i>
                                    Mixed Precision Configuration
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableMixedPrecision">
                                    <label class="form-check-label" for="enableMixedPrecision">
                                        Enable
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Simple Precision (when mixed precision is disabled) -->
                            <div id="simplePrecisionConfig">
                                <select class="form-select" id="precision">
                                    <option value="fp32">FP32 (32-bit float)</option>
                                    <option value="fp16" selected>FP16 (16-bit float)</option>
                                    <option value="bf16">BF16 (bfloat16)</option>
                                    <option value="int8">INT8 (8-bit integer)</option>
                                    <option value="fp8">FP8 (8-bit float)</option>
                                    <option value="fp4">FP4 (4-bit float)</option>
                                </select>
                                <div class="form-text">Default precision for all components</div>
                            </div>
                            
                            <!-- Mixed Precision Controls (initially hidden) -->
                            <div id="mixedPrecisionConfig" class="collapse">
                                <div class="row g-2">
                                    <div class="col-6">
                                        <label for="weightDtype" class="form-label form-label-sm">Weights</label>
                                        <select class="form-select form-select-sm" id="weightDtype">
                                            <option value="fp32">FP32</option>
                                            <option value="fp16" selected>FP16</option>
                                            <option value="bf16">BF16</option>
                                            <option value="int8">INT8</option>
                                            <option value="fp8">FP8</option>
                                            <option value="fp4">FP4</option>
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <label for="activationDtype" class="form-label form-label-sm">Activations</label>
                                        <select class="form-select form-select-sm" id="activationDtype">
                                            <option value="fp32">FP32</option>
                                            <option value="fp16" selected>FP16</option>
                                            <option value="bf16">BF16</option>
                                            <option value="int8">INT8</option>
                                            <option value="fp8">FP8</option>
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <label for="gradDtype" class="form-label form-label-sm">Gradients</label>
                                        <select class="form-select form-select-sm" id="gradDtype">
                                            <option value="fp32" selected>FP32</option>
                                            <option value="fp16">FP16</option>
                                            <option value="bf16">BF16</option>
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <label for="optimizerDtype" class="form-label form-label-sm">Optimizer</label>
                                        <select class="form-select form-select-sm" id="optimizerDtype">
                                            <option value="fp32" selected>FP32</option>
                                            <option value="fp16">FP16</option>
                                            <option value="bf16">BF16</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Configure different precisions for model components
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Training Mode Configuration -->
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="trainingMode">
                                <label class="form-check-label" for="trainingMode">
                                    <i class="fas fa-graduation-cap me-1"></i>
                                    Training Mode (includes gradients & optimizer states)
                                </label>
                            </div>
                            <div class="form-text">
                                Enable to calculate memory for training (includes gradients and optimizer states)
                            </div>
                        </div>

                        <!-- Parallel Strategy Configuration -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">
                                    <i class="fas fa-network-wired me-1"></i>
                                    Parallel Strategy
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableParallel">
                                    <label class="form-check-label" for="enableParallel">
                                        Enable
                                    </label>
                                </div>
                            </div>
                            
                            <div id="parallelConfig" class="collapse">
                                <div class="row g-2">
                                    <div class="col-6">
                                        <label for="tensorParallel" class="form-label">Tensor Parallel</label>
                                        <input type="number" class="form-control form-control-sm" 
                                               id="tensorParallel" value="1" min="1" max="64">
                                    </div>
                                    <div class="col-6">
                                        <label for="pipelineParallel" class="form-label">Pipeline Parallel</label>
                                        <input type="number" class="form-control form-control-sm" 
                                               id="pipelineParallel" value="1" min="1" max="64">
                                    </div>
                                    <div class="col-6">
                                        <label for="dataParallel" class="form-label">Data Parallel</label>
                                        <input type="number" class="form-control form-control-sm" 
                                               id="dataParallel" value="1" min="1" max="64">
                                    </div>
                                    <div class="col-6">
                                        <label for="expertParallel" class="form-label">Expert Parallel</label>
                                        <input type="number" class="form-control form-control-sm" 
                                               id="expertParallel" value="1" min="1" max="64">
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Total GPUs: <span id="totalGpus">1</span>
                                    </div>
                                    <div id="parallelValidation" class="mt-1"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Memory Analysis Controls -->
                        <div class="mb-4" id="memoryAnalysisSection">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">
                                    <i class="fas fa-memory me-1"></i>
                                    Memory Analysis
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="showTotalMemoryToggle">
                                    <label class="form-check-label" for="showTotalMemoryToggle">
                                        Show Memory
                                    </label>
                                </div>
                            </div>
                            <div class="form-text mb-3">
                                Toggle to show/hide total memory calculations and KV cache analysis
                            </div>
                            
                            <!-- KV Cache Configuration (initially hidden) -->
                            <div id="kvCacheConfig" class="collapse">
                                <!-- KV Cache Dtype Selector -->
                                <div class="mb-3">
                                    <label for="kvCacheDtype" class="form-label">
                                        <i class="fas fa-database me-1"></i>
                                        KV Cache Data Type
                                    </label>
                                    <select class="form-select" id="kvCacheDtype">
                                        <option value="fp16" selected>FP16 (16-bit float)</option>
                                        <option value="bf16">BF16 (bfloat16)</option>
                                        <option value="fp32">FP32 (32-bit float)</option>
                                        <option value="int8">INT8 (8-bit integer)</option>
                                        <option value="fp8">FP8 (8-bit float)</option>
                                    </select>
                                    <div class="form-text">
                                        Data type for KV cache memory calculations (inference only)
                                    </div>
                                </div>

                                <!-- Sequence Length Range Controls -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-arrows-alt-h me-1"></i>
                                        KV Growth Analysis Range
                                    </label>
                                    
                                    <!-- Min/Max Sequence Length -->
                                    <div class="row g-2 mb-2">
                                        <div class="col-6">
                                            <label for="minSequenceLength" class="form-label form-label-sm">Min Length</label>
                                            <input type="number" class="form-control form-control-sm" 
                                                   id="minSequenceLength" value="512" 
                                                   min="512" max="16384" step="512">
                                        </div>
                                        <div class="col-6">
                                            <label for="maxSequenceLength" class="form-label form-label-sm">Max Length</label>
                                            <input type="number" class="form-control form-control-sm" 
                                                   id="maxSequenceLength" value="32768" 
                                                   min="1024" max="32768" step="1024">
                                        </div>
                                    </div>

                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Range for KV memory growth visualization
                                    </div>
                                    <div id="sequenceLengthValidation" class="mt-1"></div>
                                </div>

                                <!-- KV Growth Visualization Button -->
                                <div class="mb-3">
                                    <button type="button" class="btn btn-outline-info btn-sm w-100" id="showKvGrowthBtn">
                                        <i class="fas fa-chart-line me-2"></i>
                                        Show KV Memory Growth Chart
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Analysis Options -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-sliders-h me-1"></i>
                                Analysis Options
                            </label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeShapes" checked>
                                <label class="form-check-label" for="includeShapes">
                                    Include matrix shape analysis
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeComparison" checked>
                                <label class="form-check-label" for="includeComparison">
                                    Include model comparison
                                </label>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-primary" id="analyzeBtn">
                                <i class="fas fa-play me-2"></i>
                                Analyze Models
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="clearBtn">
                                <i class="fas fa-trash me-2"></i>
                                Clear Selection
                            </button>
                        </div>

                        <!-- Progress Indicator -->
                        <div id="analysisProgress" class="mt-3" style="display: none;">
                            <div class="d-flex align-items-center mb-2">
                                <div class="spinner-border spinner-border-sm me-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span id="progressText">Starting analysis...</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%" 
                                     id="progressBar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Dashboard -->
            <div class="col-lg-8">
                <div id="resultsContainer">
                    <!-- Welcome Message -->
                    <div id="welcomeMessage" class="card shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">Welcome to LLM Modeling Metrics</h4>
                            <p class="text-muted">
                                Select models from the configuration panel to start analyzing 
                                computational requirements and performance characteristics.
                            </p>
                            <div class="mt-4">
                                <h6 class="text-muted mb-3">Supported Features:</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-microchip text-primary me-2"></i>
                                            <span>FLOP calculations</span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-memory text-primary me-2"></i>
                                            <span>Memory analysis</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-network-wired text-primary me-2"></i>
                                            <span>Parallel strategies</span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-chart-bar text-primary me-2"></i>
                                            <span>Model comparison</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Results will be populated here -->
                    <div id="analysisResults" style="display: none;">
                        <!-- Interactive Controls -->
                        <div id="interactiveControlsContainer" style="display: none;">
                            <!-- Interactive controls component will be rendered here -->
                        </div>
                        
                        <!-- Advanced Visualization Controls -->
                        <div id="advancedVisualizationControlsContainer" style="display: none;">
                            <!-- Advanced visualization controls component will be rendered here -->
                        </div>
                        
                        <!-- Roofline Visualization -->
                        <div id="rooflineVisualizationContainer" style="display: none;">
                            <!-- Roofline visualizer component will be rendered here -->
                        </div>
                        
                        <!-- Timing Dashboard -->
                        <div id="timingDashboardContainer" style="display: none;">
                            <!-- Timing dashboard component will be rendered here -->
                        </div>
                        
                        <!-- Optimization Suggestions -->
                        <div id="optimizationSuggestionsContainer" style="display: none;">
                            <!-- Optimization suggestions component will be rendered here -->
                        </div>
                        
                        <!-- Individual Model Results -->
                        <div id="individualResults"></div>
                        
                        <!-- Comparison Results -->
                        <div id="comparisonResults"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hardware Comparison Section -->
    <div id="hardware-comparison-section" class="container-fluid mt-4" style="display: none;">
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">
                                <i class="fas fa-balance-scale me-2"></i>
                                Multi-Hardware Comparison
                            </h4>
                            <div>
                                <button id="open-hardware-wizard" class="btn btn-light btn-sm me-2">
                                    <i class="fas fa-magic me-1"></i>
                                    Selection Wizard
                                </button>
                                <button class="btn btn-outline-light btn-sm" onclick="toggleSection('dashboard')">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Back to Dashboard
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Hardware Selector -->
                        <div id="hardware-selector-container">
                            <!-- Hardware selector will be rendered here -->
                        </div>
                        
                        <!-- Loading Indicator -->
                        <div id="comparison-loader" class="text-center py-4" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Comparing hardware platforms...</p>
                        </div>
                        
                        <!-- Error Container -->
                        <div id="error-container" style="display: none;"></div>
                        
                        <!-- Comparison Results -->
                        <div id="comparison-results" style="display: none;">
                            <!-- Results will be rendered here -->
                        </div>
                        
                        <!-- Cross-Platform Timing Analysis -->
                        <div id="cross-platform-timing-container" class="mt-4" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-stopwatch me-2"></i>
                                        Cross-Platform Timing Analysis
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <!-- Hardware Selection for Timing -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <h6>Select Hardware Platforms</h6>
                                            <div id="timing-hardware-selector">
                                                <!-- Hardware checkboxes will be rendered here -->
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div id="timing-operator-selector">
                                                <!-- Operator selector will be rendered here -->
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Analysis Controls -->
                                    <div class="text-center mb-4">
                                        <button id="analyze-timing-btn" class="btn btn-primary" disabled>
                                            <i class="fas fa-play me-1"></i>
                                            Analyze Timing
                                        </button>
                                    </div>
                                    
                                    <!-- Loading Indicator -->
                                    <div id="timing-analysis-loader" class="text-center py-4" style="display: none;">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 text-muted">Analyzing cross-platform timing...</p>
                                    </div>
                                    
                                    <!-- Error Container -->
                                    <div id="timing-error-container" style="display: none;"></div>
                                    
                                    <!-- Timing Analysis Results -->
                                    <div id="timing-analysis-results" style="display: none;">
                                        <!-- Results will be rendered here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hardware Selection Wizard Modal -->
    <div id="hardware-wizard-modal" class="hardware-wizard-modal">
        <div class="wizard-container">
            <div class="wizard-header">
                <h2>Hardware Selection Wizard</h2>
                <button class="close-btn" onclick="window.hardwareWizard?.closeWizard()">&times;</button>
            </div>
            
            <div class="wizard-progress">
                <div class="wizard-progress-container">
                    <div class="wizard-progress-bar" style="width: 25%;"></div>
                </div>
                <div class="step-indicators">
                    <div class="step-indicator active">1</div>
                    <div class="step-indicator">2</div>
                    <div class="step-indicator">3</div>
                    <div class="step-indicator">4</div>
                </div>
                <h3 id="wizard-step-title">Step 1: Use Case & Budget</h3>
            </div>
            
            <form id="wizard-form">
                <div class="wizard-content">
                    <!-- Step 1: Use Case & Budget -->
                    <div id="wizard-step-1" class="wizard-step">
                        <h3>Tell us about your use case and budget</h3>
                        
                        <div class="form-group">
                            <label for="use_case">Primary Use Case *</label>
                            <select name="use_case" id="use_case" required>
                                <option value="">Select use case...</option>
                                <option value="training">Model Training</option>
                                <option value="inference">Model Inference</option>
                                <option value="research">Research & Development</option>
                                <option value="production">Production Deployment</option>
                                <option value="general">General Purpose</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="budget">Budget (USD)</label>
                            <input type="number" name="budget" id="budget" placeholder="e.g., 25000" min="0" step="1000">
                            <small class="form-text text-muted">Leave blank if no budget constraint</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="performance_priority">Performance Priority</label>
                            <select name="performance_priority" id="performance_priority">
                                <option value="balanced">Balanced</option>
                                <option value="compute">Compute Performance</option>
                                <option value="memory">Memory Capacity</option>
                                <option value="cost">Cost Effectiveness</option>
                                <option value="efficiency">Power Efficiency</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Step 2: Performance Requirements -->
                    <div id="wizard-step-2" class="wizard-step">
                        <h3>Performance Requirements</h3>
                        
                        <div class="form-group">
                            <label for="memory_requirement">Minimum Memory (GB)</label>
                            <input type="number" name="memory_requirement" id="memory_requirement" 
                                   placeholder="e.g., 80" min="1" max="1000">
                            <small class="form-text text-muted">Minimum GPU memory required</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="power_constraint">Power Constraint (Watts)</label>
                            <input type="number" name="power_constraint" id="power_constraint" 
                                   placeholder="e.g., 400" min="50" max="1000">
                            <small class="form-text text-muted">Maximum power consumption allowed</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="form_factor">Form Factor Preference</label>
                            <select name="form_factor" id="form_factor">
                                <option value="">No preference</option>
                                <option value="pcie">PCIe Card</option>
                                <option value="oam">OAM Module</option>
                                <option value="mezzanine">Mezzanine Card</option>
                                <option value="sxm">SXM Module</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Step 3: Technical Specifications -->
                    <div id="wizard-step-3" class="wizard-step">
                        <h3>Technical Specifications</h3>
                        
                        <div class="form-group">
                            <label>Required Precision Support</label>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" name="precision_requirements" value="fp32" id="prec_fp32">
                                    <label for="prec_fp32">FP32</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="precision_requirements" value="fp16" id="prec_fp16" checked>
                                    <label for="prec_fp16">FP16</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="precision_requirements" value="bf16" id="prec_bf16">
                                    <label for="prec_bf16">BF16</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="precision_requirements" value="fp8" id="prec_fp8">
                                    <label for="prec_fp8">FP8</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="precision_requirements" value="int8" id="prec_int8">
                                    <label for="prec_int8">INT8</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="requirements-summary">
                            <h4>Requirements Summary</h4>
                            <div id="requirements-summary">
                                <p>No requirements specified yet</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 4: Recommendations -->
                    <div id="wizard-step-4" class="wizard-step">
                        <h3>Hardware Recommendations</h3>
                        
                        <div class="text-center mb-4">
                            <button type="button" id="get-recommendations-btn" class="btn btn-primary btn-lg">
                                <i class="fas fa-search me-2"></i>
                                Get Recommendations
                            </button>
                        </div>
                        
                        <!-- Loading Indicator -->
                        <div id="wizard-loader" class="text-center py-4" style="display: none;">
                            <div class="loader-spinner"></div>
                            <p class="text-muted">Analyzing your requirements...</p>
                        </div>
                        
                        <!-- Error Container -->
                        <div id="wizard-error-container" style="display: none;"></div>
                        
                        <!-- Recommendations Container -->
                        <div id="recommendations-container">
                            <!-- Recommendations will be rendered here -->
                        </div>
                    </div>
                </div>
            </form>
            
            <div class="wizard-navigation">
                <button type="button" class="prev-step-btn btn btn-secondary" style="display: none;">
                    <i class="fas fa-arrow-left me-1"></i>
                    Previous
                </button>
                <button type="button" class="next-step-btn btn btn-primary">
                    Next Step
                    <i class="fas fa-arrow-right ms-1"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div class="modal fade" id="errorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="errorMessage"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
    <script src="/static/js/hardware-selector.js?v=1"></script>
    <script src="/static/js/roofline-visualizer.js?v=3"></script>
    <script src="/static/js/timing-dashboard.js?v=1"></script>
    <script src="/static/js/optimization-suggestions.js?v=1"></script>
    <script src="/static/js/memory-controls.js?v=3"></script>
    <script src="/static/js/hardware-comparison.js?v=1"></script>
    <script src="/static/js/hardware-wizard.js?v=1"></script>
    <script src="/static/js/cross-platform-timing.js?v=1"></script>
    <script src="/static/js/interactive-controls.js?v=1"></script>
    <script src="/static/js/advanced-visualization-controls.js?v=1"></script>
    <script src="/static/js/app.js?v=4"></script>
</body>
</html>